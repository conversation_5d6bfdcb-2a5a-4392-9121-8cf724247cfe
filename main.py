"""
Main script to test the branches of the Reed-Solomon decoder.
"""
from rs_encoder import <PERSON><PERSON>ncoder
from rs_decoder import RSDecoder
import error_generator as eg
from reedsolo import ReedSolomonError

def run_branch_validation(n, k, t):
    """
    Tests specific branches of the decoder for a given RS configuration.
    """
    print(f"--- Branch Validation for n={n}, k={k}, t={t} ---")
    
    # 1. Setup
    encoder = RSEncoder(n=n, k=k)
    decoder = RSDecoder(n=n, k=k)
    original_message = [(i % 63) + 1 for i in range(k)]
    codeword = encoder.encode(original_message)
    
    test_cases = [
        # Case 1: No errors
        eg.generate_no_error_mask(n),
        
        # Case 2: Correctable errors
        eg.generate_correctable_error_mask(n, t),
        
        # Case 3: Uncorrectable (too many) errors
        eg.generate_uncorrectable_too_many_errors_mask(n, t),
        
        # Case 4: Potential failed correction
        eg.generate_failed_correction_mask(codeword, encoder, t)
    ]
    
    # 2. Run tests
    for case in test_cases:
        if case is None: continue
        
        desc = case['desc']
        mask = case['mask']
        expected = case.get('expected_outcome', 'success')
        
        print(f"  Testing branch: '{desc}' (expecting: {expected})")
        
        errored_codeword = [c ^ m for c, m in zip(codeword, mask)]
        
        try:
            decoded_message = decoder.decode(errored_codeword)
            
            if expected == 'success':
                if decoded_message == original_message:
                    print("    PASS: Successfully decoded.")
                else:
                    print("    FAIL: Decoded message does not match original.")
            else:
                print(f"    FAIL: Expected an error, but decoding succeeded.")

        except ReedSolomonError as e:
            error_str = str(e).lower()
            if expected == 'too_many_errors' and 'too many errors' in error_str:
                print("    PASS: Caught expected 'Too many errors' exception.")
            elif expected == 'correction_failed' and 'could not correct' in error_str:
                print("    PASS: Caught expected 'Could not correct message' exception.")
            else:
                print(f"    FAIL: Caught an unexpected ReedSolomonError: {e}")
        except Exception as e:
            print(f"    FAIL: Caught an unexpected exception: {e}")

def main():
    """
    Main function to run branch validation for all configurations.
    """
    configs = [
        (1, 22, 24),
        (1, 44, 46),
        (2, 44, 48)
    ]

    for t, k, n in configs:
        run_branch_validation(n=n, k=k, t=t)
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main()
