"""
演示如何使用分支特定的错误模式生成函数
"""
import error_generator as eg
from rs_encoder import RSEncoder
from rs_decoder import RSDecoder
from reedsolo import ReedSolomonError


def demonstrate_branch_specific_patterns():
    """演示每个分支的特定错误模式生成和测试"""
    
    print("="*80)
    print("Reed-Solomon 分支特定错误模式演示")
    print("="*80)
    
    # 使用配置 n=24, k=22, t=1
    n, k, t = 24, 22, 1
    encoder = RSEncoder(n=n, k=k)
    decoder = RSDecoder(n=n, k=k)
    
    # 生成测试消息
    message = [(i % 63) + 1 for i in range(k)]
    codeword = encoder.encode(message)
    
    print(f"测试配置: n={n}, k={k}, t={t}")
    print(f"原始消息长度: {len(message)}")
    print(f"编码后长度: {len(codeword)}")
    print()
    
    # 获取所有分支的错误模式
    branch_patterns = eg.generate_all_branch_patterns(n, k, t)
    
    print("🧪 分支特定错误模式测试:")
    print("-" * 80)
    
    for i, (branch_name, pattern) in enumerate(sorted(branch_patterns.items()), 1):
        print(f"\n{i}. 测试分支: {branch_name}")
        print(f"   模式描述: {pattern['desc']}")
        print(f"   目标分支: {pattern['target_branch']}")
        print(f"   期望结果: {pattern['expected_outcome']}")
        
        # 应用错误掩码
        mask = pattern['mask']
        error_count = sum(1 for x in mask if x != 0)
        print(f"   错误数量: {error_count}")
        
        errored_codeword = [c ^ m for c, m in zip(codeword, mask)]
        
        # 测试解码
        try:
            decoded_message = decoder.decode(errored_codeword)
            
            if decoded_message == message:
                result = "✅ 成功解码，消息正确"
            else:
                result = "⚠️  解码成功，但消息不匹配"
                
        except ReedSolomonError as e:
            result = f"❌ Reed-Solomon错误: {str(e)[:50]}..."
            
        except Exception as e:
            result = f"❌ 其他错误: {str(e)[:50]}..."
        
        print(f"   测试结果: {result}")


def demonstrate_individual_branch_functions():
    """演示单独调用每个分支生成函数"""
    
    print("\n" + "="*80)
    print("单独分支生成函数演示")
    print("="*80)
    
    n, k, t = 48, 44, 2  # 使用支持所有分支的配置
    
    # 所有分支生成函数
    branch_functions = {
        'syndrome_calculation': eg.generate_syndrome_calculation_pattern,
        'syndrome_zero_check': eg.generate_syndrome_zero_check_pattern,
        'error_locator_degree_1': eg.generate_error_locator_degree_1_pattern,
        'error_locator_degree_2': eg.generate_error_locator_degree_2_pattern,
        'chien_search': eg.generate_chien_search_pattern,
        'forney_algorithm': eg.generate_forney_algorithm_pattern,
        'correction_verification': eg.generate_correction_verification_pattern,
        'too_many_errors_exception': eg.generate_too_many_errors_exception_pattern,
        'correction_failed_exception': eg.generate_correction_failed_exception_pattern
    }
    
    print(f"使用配置: n={n}, k={k}, t={t}")
    print()
    
    for i, (branch_name, func) in enumerate(branch_functions.items(), 1):
        print(f"{i}. 分支: {branch_name}")
        
        try:
            pattern = func(n, k, t)
            if pattern is not None:
                error_count = sum(1 for x in pattern['mask'] if x != 0)
                print(f"   ✅ 生成成功")
                print(f"   📝 描述: {pattern['desc']}")
                print(f"   🎯 目标: {pattern['target_branch']}")
                print(f"   🔢 错误数: {error_count}")
                print(f"   📊 期望: {pattern['expected_outcome']}")
            else:
                print(f"   ⚠️  返回 None (可能不适用于当前配置)")
        except Exception as e:
            print(f"   ❌ 生成失败: {e}")
        print()


def demonstrate_get_specific_pattern():
    """演示通过分支名称获取特定模式"""
    
    print("="*80)
    print("通过分支名称获取特定模式演示")
    print("="*80)
    
    n, k, t = 24, 22, 1
    
    # 演示获取特定分支的模式
    target_branches = [
        'syndrome_zero_check',
        'error_locator_degree_1', 
        'chien_search',
        'too_many_errors_exception'
    ]
    
    print(f"配置: n={n}, k={k}, t={t}")
    print(f"演示分支: {', '.join(target_branches)}")
    print()
    
    for branch_name in target_branches:
        print(f"🎯 获取分支: {branch_name}")
        
        try:
            pattern = eg.get_branch_specific_pattern(branch_name, n, k, t)
            if pattern is not None:
                error_count = sum(1 for x in pattern['mask'] if x != 0)
                print(f"   ✅ 成功获取模式")
                print(f"   📝 {pattern['desc']}")
                print(f"   🔢 错误数: {error_count}")
                print(f"   📊 期望结果: {pattern['expected_outcome']}")
            else:
                print(f"   ⚠️  返回 None")
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
        print()


def demonstrate_multiple_variants():
    """演示生成多个变体模式"""
    
    print("="*80)
    print("多变体模式生成演示")
    print("="*80)
    
    n, k, t = 24, 22, 1
    num_variants = 2
    
    print(f"配置: n={n}, k={k}, t={t}")
    print(f"每个分支生成 {num_variants} 个变体")
    print()
    
    try:
        patterns = eg.generate_multiple_patterns_per_branch(n, k, t, num_variants)
        
        print(f"📊 总共生成 {len(patterns)} 个错误模式变体")
        
        # 按分支分组
        branch_groups = {}
        for pattern in patterns:
            target_branch = pattern.get('target_branch', 'unknown')
            if target_branch not in branch_groups:
                branch_groups[target_branch] = []
            branch_groups[target_branch].append(pattern)
        
        print(f"📋 分组统计:")
        for branch, group_patterns in sorted(branch_groups.items()):
            print(f"   {branch}: {len(group_patterns)} 个变体")
            for j, pattern in enumerate(group_patterns, 1):
                error_count = sum(1 for x in pattern['mask'] if x != 0)
                print(f"      {j}. {pattern['desc']} (错误数: {error_count})")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")


def main():
    """主函数"""
    demonstrate_branch_specific_patterns()
    demonstrate_individual_branch_functions()
    demonstrate_get_specific_pattern()
    demonstrate_multiple_variants()
    
    print("\n" + "="*80)
    print("演示完成！")
    print("="*80)
    print("\n💡 使用提示:")
    print("1. 使用 generate_all_branch_patterns(n, k, t) 获取所有分支的错误模式")
    print("2. 使用 get_branch_specific_pattern(branch_name, n, k, t) 获取特定分支的模式")
    print("3. 使用 generate_multiple_patterns_per_branch() 生成多个变体以提高覆盖率")
    print("4. 每个生成的模式都包含 'type', 'desc', 'mask', 'target_branch', 'expected_outcome' 字段")
    print("5. 错误掩码可以直接与编码后的码字进行XOR操作来注入错误")


if __name__ == "__main__":
    main()
