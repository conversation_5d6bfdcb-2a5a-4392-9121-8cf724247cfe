"""
测试每个目标分支都有对应的错误模式生成函数
"""
import error_generator as eg
from branch_validator import BranchCoverageValidator


def test_all_branches_have_generators():
    """测试所有目标分支都有对应的生成函数"""
    
    # 获取所有目标分支
    validator = BranchCoverageValidator()
    target_branches = validator.target_branches
    
    print("目标分支列表:")
    for i, branch in enumerate(sorted(target_branches), 1):
        print(f"  {i}. {branch}")
    
    print(f"\n总共 {len(target_branches)} 个目标分支")
    
    # 测试配置
    test_configs = [
        (24, 22, 1),
        (46, 44, 1), 
        (48, 44, 2)
    ]
    
    print("\n" + "="*60)
    print("测试每个分支的错误模式生成")
    print("="*60)
    
    for n, k, t in test_configs:
        print(f"\n配置: n={n}, k={k}, t={t}")
        print("-" * 40)
        
        # 生成所有分支的错误模式
        try:
            branch_patterns = eg.generate_all_branch_patterns(n, k, t)
            
            print(f"成功生成 {len(branch_patterns)} 个分支的错误模式:")
            
            for branch_name in sorted(target_branches):
                if branch_name in branch_patterns:
                    pattern = branch_patterns[branch_name]
                    error_count = sum(1 for x in pattern['mask'] if x != 0)
                    print(f"  ✅ {branch_name}: {pattern['desc']} (错误数: {error_count})")
                else:
                    print(f"  ❌ {branch_name}: 未生成错误模式")
            
            # 检查覆盖率
            coverage = len(branch_patterns) / len(target_branches) * 100
            print(f"\n分支覆盖率: {coverage:.1f}% ({len(branch_patterns)}/{len(target_branches)})")
            
        except Exception as e:
            print(f"错误: {e}")


def test_individual_branch_generators():
    """测试每个分支生成函数的独立调用"""
    
    print("\n" + "="*60)
    print("测试单独的分支生成函数")
    print("="*60)
    
    # 测试配置
    n, k, t = 24, 22, 1
    
    # 所有分支生成函数
    branch_functions = {
        'syndrome_calculation': eg.generate_syndrome_calculation_pattern,
        'syndrome_zero_check': eg.generate_syndrome_zero_check_pattern,
        'error_locator_degree_1': eg.generate_error_locator_degree_1_pattern,
        'error_locator_degree_2': eg.generate_error_locator_degree_2_pattern,
        'chien_search': eg.generate_chien_search_pattern,
        'forney_algorithm': eg.generate_forney_algorithm_pattern,
        'correction_verification': eg.generate_correction_verification_pattern,
        'too_many_errors_exception': eg.generate_too_many_errors_exception_pattern,
        'correction_failed_exception': eg.generate_correction_failed_exception_pattern
    }
    
    print(f"使用配置: n={n}, k={k}, t={t}")
    print()
    
    for branch_name, func in branch_functions.items():
        try:
            pattern = func(n, k, t)
            if pattern is not None:
                error_count = sum(1 for x in pattern['mask'] if x != 0)
                print(f"✅ {branch_name}:")
                print(f"   描述: {pattern['desc']}")
                print(f"   目标分支: {pattern['target_branch']}")
                print(f"   错误数: {error_count}")
                print(f"   期望结果: {pattern['expected_outcome']}")
            else:
                print(f"⚠️  {branch_name}: 返回 None (可能不适用于当前配置)")
        except Exception as e:
            print(f"❌ {branch_name}: 错误 - {e}")
        print()


def test_get_branch_specific_pattern():
    """测试通过分支名称获取特定模式的函数"""
    
    print("\n" + "="*60)
    print("测试 get_branch_specific_pattern 函数")
    print("="*60)
    
    validator = BranchCoverageValidator()
    target_branches = validator.target_branches
    
    n, k, t = 24, 22, 1
    
    for branch_name in sorted(target_branches):
        try:
            pattern = eg.get_branch_specific_pattern(branch_name, n, k, t)
            if pattern is not None:
                error_count = sum(1 for x in pattern['mask'] if x != 0)
                print(f"✅ {branch_name}: {pattern['desc']} (错误数: {error_count})")
            else:
                print(f"⚠️  {branch_name}: 返回 None")
        except Exception as e:
            print(f"❌ {branch_name}: {e}")


def test_multiple_patterns_generation():
    """测试生成多个变体模式"""
    
    print("\n" + "="*60)
    print("测试多变体模式生成")
    print("="*60)
    
    n, k, t = 24, 22, 1
    
    try:
        patterns = eg.generate_multiple_patterns_per_branch(n, k, t, num_patterns_per_branch=2)
        
        print(f"生成了 {len(patterns)} 个错误模式变体")
        
        # 按分支分组统计
        branch_counts = {}
        for pattern in patterns:
            target_branch = pattern.get('target_branch', 'unknown')
            branch_counts[target_branch] = branch_counts.get(target_branch, 0) + 1
        
        print("\n每个分支的模式数量:")
        for branch, count in sorted(branch_counts.items()):
            print(f"  {branch}: {count} 个模式")
            
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    test_all_branches_have_generators()
    test_individual_branch_generators()
    test_get_branch_specific_pattern()
    test_multiple_patterns_generation()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)
