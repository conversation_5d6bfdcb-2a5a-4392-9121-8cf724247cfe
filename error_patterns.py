"""
Systematic error pattern generation for comprehensive branch coverage.
"""
import itertools
from typing import List, Dict, Tuple
from gf import GF64

class ErrorPatternGenerator:
    def __init__(self, n: int, k: int, t: int):
        self.n = n  # total symbols
        self.k = k  # info symbols
        self.t = t  # error tolerance
        self.gf = GF64()

    def generate_syndrome_patterns(self) -> List[Dict]:
        """生成针对综合征计算的错误模式"""
        patterns = []

        # 1. 零综合征 (无错误)
        patterns.append({
            'type': 'zero_syndrome',
            'mask': [0] * self.n,
            'target_branch': 'syndrome_zero'
        })

        # 2. 单一综合征分量非零
        for i in range(2 * self.t):
            mask = [0] * self.n
            mask[0] = self.gf.alpha_power(i + 1)
            patterns.append({
                'type': f'single_syndrome_s{i}',
                'mask': mask,
                'target_branch': f'syndrome_component_{i}'
            })

        return patterns

    def generate_locator_polynomial_patterns(self) -> List[Dict]:
        """生成针对错误定位多项式的错误模式"""
        patterns = []

        # 1. 单错误模式 (一次多项式)
        for pos in range(self.n):
            mask = [0] * self.n
            mask[pos] = self.gf.random_nonzero()
            patterns.append({
                'type': f'single_error_pos_{pos}',
                'mask': mask,
                'target_branch': 'linear_locator'
            })

        # 2. 双错误模式 (二次多项式)
        if self.t >= 2:
            for pos1, pos2 in itertools.combinations(range(self.n), 2):
                mask = [0] * self.n
                mask[pos1] = self.gf.random_nonzero()
                mask[pos2] = self.gf.random_nonzero()
                patterns.append({
                    'type': f'double_error_pos_{pos1}_{pos2}',
                    'mask': mask,
                    'target_branch': 'quadratic_locator'
                })

        return patterns

    def generate_boundary_patterns(self) -> List[Dict]:
        """生成边界条件错误模式"""
        patterns = []

        # 1. 恰好t个错误
        for positions in itertools.combinations(range(self.n), self.t):
            mask = [0] * self.n
            for pos in positions:
                mask[pos] = self.gf.random_nonzero()
            patterns.append({
                'type': f'exactly_{self.t}_errors',
                'mask': mask,
                'target_branch': 'max_correctable'
            })

        # 2. t+1个错误 (不可纠正)
        for positions in itertools.combinations(range(self.n), self.t + 1):
            mask = [0] * self.n
            for pos in positions:
                mask[pos] = self.gf.random_nonzero()
            patterns.append({
                'type': f'{self.t + 1}_errors_uncorrectable',
                'mask': mask,
                'target_branch': 'too_many_errors'
            })

        return patterns