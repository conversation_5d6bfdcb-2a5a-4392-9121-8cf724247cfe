"""
Module for Reed-Solomon Encoder using the 'reedsolo' library.
"""
import reedsolo as rs
from gf import PRIMITIVE_POLY_INT, SYMBOL_BIT_LENGTH

class RSEncoder:
    """
    A class to perform Reed-Solomon encoding using the reedsolo library.
    """
    def __init__(self, n, k):
        """
        Initializes the encoder.
        n: total number of symbols in a codeword (info + parity)
        k: number of information symbols
        """
        if n <= k:
            raise ValueError("Total symbols (n) must be greater than info symbols (k).")
        
        self.n = n
        self.k = k
        self.nsym = n - k # Number of parity symbols
        
        # Initialize the Reed-Solomon codec
        # c_exp is the symbol bit length (6 for GF(2^6))
        # prim is the primitive polynomial
        self.codec = rs.RSCodec(self.nsym, c_exp=SYMBOL_BIT_LENGTH, prim=PRIMITIVE_POLY_INT)

    def encode(self, message):
        """
        Encodes a message by adding parity symbols.
        message: a list of integers representing the information symbols.
        """
        if len(message) != self.k:
            raise ValueError(f"Message length must be {self.k}, but got {len(message)}.")
        
        # The library expects a bytearray or bytes
        message_bytes = bytearray(message)
        
        # The encode method returns the full codeword (message + parity)
        encoded = self.codec.encode(message_bytes)
        
        return list(encoded)