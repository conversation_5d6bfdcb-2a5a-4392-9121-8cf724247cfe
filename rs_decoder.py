"""
Module for Reed-Solomon Decoder using the 'reedsolo' library.
"""
import reedsolo as rs
from gf import PRIMITIVE_POLY_INT, SYMBOL_BIT_LENGTH

class RSDecoder:
    """
    A class to perform Reed-Solomon decoding using the reedsolo library.
    """
    def __init__(self, n, k):
        """
        Initializes the decoder.
        n: total number of symbols in a codeword (info + parity)
        k: number of information symbols
        """
        if n <= k:
            raise ValueError("Total symbols (n) must be greater than info symbols (k).")
        
        self.n = n
        self.k = k
        self.nsym = n - k # Number of parity symbols
        
        # Initialize the Reed-Solomon codec
        self.codec = rs.RSCodec(self.nsym, c_exp=SYMBOL_BIT_LENGTH, prim=PRIMITIVE_POLY_INT)

    def decode(self, received_message):
        """
        Decodes a received message and corrects errors.
        received_message: a list of integers representing the received codeword.
        Returns the corrected message (info symbols only).
        """
        if len(received_message) != self.n:
            raise ValueError(f"Received message length must be {self.n}, but got {len(received_message)}.")
        
        try:
            # The library expects a bytearray or bytes
            received_bytes = bytearray(received_message)
            
            # The decode method returns the corrected message, corrected parity, and error locations
            decoded_msg, _, errata_pos = self.codec.decode(received_bytes)
            
            # The library may return a bytearray view, so convert to list
            return list(decoded_msg)
            
        except rs.ReedSolomonError as e:
            # This exception is raised if the message is uncorrectable
            raise ValueError(f"Too many errors to correct: {e}")