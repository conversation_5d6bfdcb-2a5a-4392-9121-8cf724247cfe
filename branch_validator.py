"""
Branch coverage validation for RS decoder testing.
"""
import coverage
from typing import Set, Dict, List
from rs_decoder import RSDecoder

class BranchCoverageValidator:
    def __init__(self):
        self.covered_branches = set()
        self.target_branches = {
            'syndrome_calculation',
            'syndrome_zero_check',
            'error_locator_degree_1',
            'error_locator_degree_2',
            'chien_search',
            'forney_algorithm',
            'correction_verification',
            'too_many_errors_exception',
            'correction_failed_exception'
        }

    def run_with_coverage(self, test_cases: List[Dict]) -> Dict:
        """运行测试用例并收集分支覆盖信息"""
        cov = coverage.Coverage(branch=True)
        cov.start()

        results = {}
        for case in test_cases:
            try:
                # 执行解码测试
                result = self._execute_test_case(case)
                results[case['type']] = result
                self._analyze_branch_hit(case, result)
            except Exception as e:
                results[case['type']] = {'error': str(e)}

        cov.stop()
        cov.save()

        return {
            'test_results': results,
            'coverage_report': self._generate_coverage_report(cov),
            'branch_coverage': self._calculate_branch_coverage()
        }

    def _execute_test_case(self, case: Dict) -> Dict:
        """执行单个测试用例"""
        from rs_encoder import RSEncoder
        from rs_decoder import RSDecoder
        from reedsolo import ReedSolomonError

        # 根据测试用例类型确定配置
        if 'n24' in case.get('type', '') or case.get('type', '').endswith('_24'):
            n, k, t = 24, 22, 1
        elif 'n46' in case.get('type', '') or case.get('type', '').endswith('_46'):
            n, k, t = 46, 44, 1
        elif 'n48' in case.get('type', '') or case.get('type', '').endswith('_48'):
            n, k, t = 48, 44, 2
        else:
            # 默认配置
            n, k, t = 24, 22, 1

        encoder = RSEncoder(n=n, k=k)
        decoder = RSDecoder(n=n, k=k)

        # 生成测试消息
        original_message = [(i % 63) + 1 for i in range(k)]
        codeword = encoder.encode(original_message)

        # 应用错误掩码
        mask = case.get('mask', [0] * n)
        errored_codeword = [c ^ m for c, m in zip(codeword, mask)]

        result = {
            'test_type': case.get('type', 'unknown'),
            'target_branch': case.get('target_branch', 'unknown'),
            'error_count': sum(1 for m in mask if m != 0),
            'success': False,
            'error_message': None,
            'decoded_correctly': False
        }

        try:
            decoded_message = decoder.decode(errored_codeword)
            result['success'] = True
            result['decoded_correctly'] = (decoded_message == original_message)

        except ReedSolomonError as e:
            result['error_message'] = str(e)
            result['exception_type'] = 'ReedSolomonError'

        except Exception as e:
            result['error_message'] = str(e)
            result['exception_type'] = type(e).__name__

        return result

    def _analyze_branch_hit(self, case: Dict, result: Dict):
        """分析测试用例命中的分支"""
        target_branch = case.get('target_branch', '')

        # 根据测试结果和目标分支更新覆盖情况
        if target_branch == 'syndrome_zero' and result.get('success') and result.get('decoded_correctly'):
            self.covered_branches.add('syndrome_zero_check')

        elif target_branch.startswith('syndrome_component_'):
            self.covered_branches.add('syndrome_calculation')

        elif target_branch == 'linear_locator':
            self.covered_branches.add('error_locator_degree_1')
            self.covered_branches.add('chien_search')
            if result.get('success'):
                self.covered_branches.add('forney_algorithm')
                self.covered_branches.add('correction_verification')

        elif target_branch == 'quadratic_locator':
            self.covered_branches.add('error_locator_degree_2')
            self.covered_branches.add('chien_search')
            if result.get('success'):
                self.covered_branches.add('forney_algorithm')
                self.covered_branches.add('correction_verification')

        elif target_branch == 'too_many_errors':
            if result.get('exception_type') == 'ReedSolomonError':
                self.covered_branches.add('too_many_errors_exception')

        elif target_branch == 'max_correctable':
            if result.get('success'):
                self.covered_branches.add('correction_verification')

    def _generate_coverage_report(self, cov) -> Dict:
        """生成覆盖率报告"""
        try:
            # 获取覆盖率数据
            cov_data = cov.get_data()

            # 生成简单的覆盖率报告
            report = {
                'files_covered': list(cov_data.measured_files()),
                'branch_coverage_percentage': self._calculate_branch_coverage(),
                'covered_branches': list(self.covered_branches),
                'uncovered_branches': list(self.target_branches - self.covered_branches)
            }

            return report

        except Exception as e:
            return {
                'error': f"Failed to generate coverage report: {e}",
                'branch_coverage_percentage': self._calculate_branch_coverage(),
                'covered_branches': list(self.covered_branches),
                'uncovered_branches': list(self.target_branches - self.covered_branches)
            }

    def _calculate_branch_coverage(self) -> float:
        """计算分支覆盖率"""
        covered = len(self.covered_branches)
        total = len(self.target_branches)
        return (covered / total) * 100 if total > 0 else 0