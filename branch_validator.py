"""
Branch coverage validation for RS decoder testing.
"""
import coverage
from typing import Set, Dict, List
from rs_decoder import RSDecoder

class BranchCoverageValidator:
    def __init__(self):
        self.covered_branches = set()
        self.target_branches = {
            'syndrome_calculation',
            'syndrome_zero_check',
            'error_locator_degree_1',
            'error_locator_degree_2',
            'chien_search',
            'forney_algorithm',
            'correction_verification',
            'too_many_errors_exception',
            'correction_failed_exception'
        }

    def run_with_coverage(self, test_cases: List[Dict]) -> Dict:
        """运行测试用例并收集分支覆盖信息"""
        cov = coverage.Coverage(branch=True)
        cov.start()

        results = {}
        for case in test_cases:
            try:
                # 执行解码测试
                result = self._execute_test_case(case)
                results[case['type']] = result
                self._analyze_branch_hit(case, result)
            except Exception as e:
                results[case['type']] = {'error': str(e)}

        cov.stop()
        cov.save()

        return {
            'test_results': results,
            'coverage_report': self._generate_coverage_report(cov),
            'branch_coverage': self._calculate_branch_coverage()
        }

    def _calculate_branch_coverage(self) -> float:
        """计算分支覆盖率"""
        covered = len(self.covered_branches)
        total = len(self.target_branches)
        return (covered / total) * 100 if total > 0 else 0