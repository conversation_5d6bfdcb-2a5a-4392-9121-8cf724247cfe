"""
Configuration for the Reed-Solomon library.
"""
# GF(2^6) is defined by the primitive polynomial x^6 + x + 1
# The integer representation is 1*x^6 + 0*x^5 + 0*x^4 + 0*x^3 + 0*x^2 + 1*x^1 + 1*x^0
# which is 0b1000011 = 67
PRIMITIVE_POLY_INT = 67
SYMBOL_BIT_LENGTH = 6

"""
Complete GF(2^6) field operations for error pattern generation.
"""
import random

class GF64:
    def __init__(self):
        self.prim_poly = 67  # x^6 + x + 1
        self.field_size = 64
        self._build_tables()

    def _build_tables(self):
        """构建对数和指数表"""
        self.exp_table = [0] * (self.field_size * 2)
        self.log_table = [0] * self.field_size

        x = 1
        for i in range(self.field_size - 1):
            self.exp_table[i] = x
            self.log_table[x] = i
            x = self._multiply_by_alpha(x)

        # 扩展表以避免模运算
        for i in range(self.field_size - 1, self.field_size * 2 - 1):
            self.exp_table[i] = self.exp_table[i - (self.field_size - 1)]

    def alpha_power(self, power: int) -> int:
        """计算α的幂次"""
        if power == 0:
            return 1
        return self.exp_table[power % (self.field_size - 1)]

    def random_nonzero(self) -> int:
        """生成随机非零元素"""
        return random.randint(1, self.field_size - 1)
