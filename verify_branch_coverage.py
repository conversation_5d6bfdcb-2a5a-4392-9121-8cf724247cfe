"""
验证所有目标分支都有对应的错误模式生成函数
"""
import error_generator as eg
from branch_validator import BranchCoverageValidator


def main():
    print("="*80)
    print("Reed-Solomon 分支覆盖验证报告")
    print("="*80)
    
    # 获取所有目标分支
    validator = BranchCoverageValidator()
    target_branches = validator.target_branches
    
    print(f"\n📋 目标分支列表 (共 {len(target_branches)} 个):")
    for i, branch in enumerate(sorted(target_branches), 1):
        print(f"   {i:2d}. {branch}")
    
    # 测试配置
    test_configs = [
        (24, 22, 1, "小包，低错误率"),
        (46, 44, 1, "中包，低错误率"), 
        (48, 44, 2, "中包，高错误率")
    ]
    
    print(f"\n🧪 测试配置 (共 {len(test_configs)} 个):")
    for i, (n, k, t, desc) in enumerate(test_configs, 1):
        print(f"   {i}. n={n}, k={k}, t={t} - {desc}")
    
    print("\n" + "="*80)
    print("分支生成函数验证结果")
    print("="*80)
    
    # 验证每个配置
    overall_results = {}
    
    for n, k, t, desc in test_configs:
        config_name = f"n{n}_k{k}_t{t}"
        print(f"\n🔍 配置: {config_name} ({desc})")
        print("-" * 60)
        
        # 生成所有分支的错误模式
        try:
            branch_patterns = eg.generate_all_branch_patterns(n, k, t)
            
            results = {}
            for branch_name in sorted(target_branches):
                if branch_name in branch_patterns:
                    pattern = branch_patterns[branch_name]
                    error_count = sum(1 for x in pattern['mask'] if x != 0)
                    results[branch_name] = {
                        'status': '✅ 成功',
                        'pattern': pattern['desc'],
                        'error_count': error_count,
                        'expected': pattern['expected_outcome']
                    }
                    print(f"   ✅ {branch_name:<30} | 错误数: {error_count:2d} | 期望: {pattern['expected_outcome']}")
                else:
                    results[branch_name] = {
                        'status': '❌ 缺失',
                        'pattern': None,
                        'error_count': 0,
                        'expected': None
                    }
                    print(f"   ❌ {branch_name:<30} | 未生成错误模式")
            
            # 统计
            success_count = len(branch_patterns)
            total_count = len(target_branches)
            coverage_rate = success_count / total_count * 100
            
            print(f"\n   📊 覆盖统计: {success_count}/{total_count} ({coverage_rate:.1f}%)")
            
            overall_results[config_name] = {
                'results': results,
                'coverage_rate': coverage_rate,
                'success_count': success_count,
                'total_count': total_count
            }
            
        except Exception as e:
            print(f"   ❌ 配置测试失败: {e}")
            overall_results[config_name] = {'error': str(e)}
    
    # 总体报告
    print("\n" + "="*80)
    print("总体验证报告")
    print("="*80)
    
    total_configs = len(test_configs)
    successful_configs = 0
    total_coverage = 0
    
    print(f"\n📈 各配置覆盖率:")
    for config_name, result in overall_results.items():
        if 'error' not in result:
            coverage = result['coverage_rate']
            success = result['success_count']
            total = result['total_count']
            print(f"   {config_name:<12}: {coverage:5.1f}% ({success}/{total})")
            successful_configs += 1
            total_coverage += coverage
        else:
            print(f"   {config_name:<12}: 错误 - {result['error']}")
    
    if successful_configs > 0:
        avg_coverage = total_coverage / successful_configs
        print(f"\n🎯 平均覆盖率: {avg_coverage:.1f}%")
        print(f"📊 成功配置: {successful_configs}/{total_configs}")
    
    # 分支覆盖详情
    print(f"\n📋 分支覆盖详情:")
    branch_coverage_summary = {}
    
    for branch in sorted(target_branches):
        covered_configs = []
        for config_name, result in overall_results.items():
            if 'error' not in result and branch in result['results']:
                if result['results'][branch]['status'].startswith('✅'):
                    covered_configs.append(config_name)
        
        coverage_count = len(covered_configs)
        branch_coverage_summary[branch] = {
            'covered_configs': covered_configs,
            'coverage_count': coverage_count
        }
        
        if coverage_count == total_configs:
            status = "✅ 全覆盖"
        elif coverage_count > 0:
            status = f"⚠️  部分覆盖 ({coverage_count}/{total_configs})"
        else:
            status = "❌ 未覆盖"
        
        print(f"   {branch:<30} | {status}")
        if coverage_count > 0 and coverage_count < total_configs:
            print(f"      覆盖配置: {', '.join(covered_configs)}")
    
    # 结论
    print("\n" + "="*80)
    print("验证结论")
    print("="*80)
    
    fully_covered_branches = sum(1 for b in branch_coverage_summary.values() if b['coverage_count'] == total_configs)
    partially_covered_branches = sum(1 for b in branch_coverage_summary.values() if 0 < b['coverage_count'] < total_configs)
    uncovered_branches = sum(1 for b in branch_coverage_summary.values() if b['coverage_count'] == 0)
    
    print(f"\n📊 分支覆盖统计:")
    print(f"   ✅ 全覆盖分支: {fully_covered_branches}/{len(target_branches)}")
    print(f"   ⚠️  部分覆盖分支: {partially_covered_branches}/{len(target_branches)}")
    print(f"   ❌ 未覆盖分支: {uncovered_branches}/{len(target_branches)}")
    
    if uncovered_branches == 0:
        print(f"\n🎉 结论: 所有 {len(target_branches)} 个目标分支都有对应的错误模式生成函数！")
        print("   项目已满足要求：每个分支都有直接可以生成对应错误pattern的函数。")
    else:
        print(f"\n⚠️  结论: 还有 {uncovered_branches} 个分支需要完善错误模式生成函数。")
    
    print(f"\n📈 整体评估:")
    if successful_configs == total_configs:
        print("   ✅ 所有配置都能正常生成错误模式")
    else:
        print(f"   ⚠️  {total_configs - successful_configs} 个配置存在问题")
    
    if avg_coverage >= 90:
        print("   ✅ 分支覆盖率优秀 (≥90%)")
    elif avg_coverage >= 70:
        print("   ⚠️  分支覆盖率良好 (70-90%)")
    else:
        print("   ❌ 分支覆盖率需要改进 (<70%)")


if __name__ == "__main__":
    main()
