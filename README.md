# RS FEC Error Injection

## Task

Reed-Solomon Forward Correction Code Error Injection Functionality

## Algorithm Details

- System code variant of Reed-Solomon code in format of
  `<information symbols>+<parity symbols>`
- Symbol bit length: 6
- Primitive polynomial: $x^6 + x + 1$
  - based on this, all elements in this GF(2^6) should be represented
    in 6bits binary format: bit of corresponding power is set 1
- Error tolerance: 1 or 2
- Generator polynomial:
  - t = 1: $(x+1)(x+a)$ (the final result is $x^2+3x+2$)
  - t = 2: $(x+1)(x+a)(x+a^2)(x+a^3)$ (calculate the final result)
- Information symbol number in a package: 22 or 44
- Total 3 combinations:

| tolerance | information symbol number | symbol number after encoding |
|:---------:|:-------------------------:|:----------------------------:|
|     1     |             22            |              24              |
|     1     |             44            |              46              |
|     2     |             44            |              48              |

## Outputs

### goals

There are many kinds of criterion in the decoding algorithm.
And our goals are to cover all the decision branches by injecting
different kinds of errors.

### Programming Language Specific

The language shall be Python only.
All 3rd party packages are welcome.
Always use `venv`.

### Error Injection Format

I expect the error masks of each symbol (including the
parity symbol), which can be XORed to the original
encoded symbols to produce error symbols.
