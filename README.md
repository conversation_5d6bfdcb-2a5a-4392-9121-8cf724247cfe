# RS FEC Error Injection

## Task

Reed-Solomon Forward Correction Code Error Injection Functionality

## Algorithm Details

- System code variant of Reed-Solomon code in format of
  `<information symbols>+<parity symbols>`
- Symbol bit length: 6
- Primitive polynomial: $x^6 + x + 1$
  - based on this, all elements in this GF(2^6) should be represented
    in 6bits binary format: bit of corresponding power is set 1
- Error tolerance: 1 or 2
- Generator polynomial:
  - t = 1: $(x+1)(x+a)$ (the final result is $x^2+3x+2$)
  - t = 2: $(x+1)(x+a)(x+a^2)(x+a^3)$ (calculate the final result)
- Information symbol number in a package: 22 or 44
- Total 3 combinations:

| tolerance | information symbol number | symbol number after encoding |
|:---------:|:-------------------------:|:----------------------------:|
|     1     |             22            |              24              |
|     1     |             44            |              46              |
|     2     |             44            |              48              |

## Outputs

### goals

There are many kinds of criterion in the decoding algorithm.
And our goals are to cover all the decision branches by injecting
different kinds of errors.

### Programming Language Specific

The language shall be Python only.
All 3rd party packages are welcome.
Always use `venv`.

### Error Injection Format

I expect the error masks of each symbol (including the
parity symbol), which can be XORed to the original
encoded symbols to produce error symbols.

## Installation and Setup

### Prerequisites
- Python 3.7 or higher
- Virtual environment (recommended)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd /path/to/reedsolo
   ```

2. **Set up virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Basic Usage

#### 1. Run Basic Branch Validation Tests
```bash
python main.py
```
This will run basic tests for all three RS configurations and show which branches are being tested.

#### 2. Run Comprehensive Branch Coverage Analysis
```bash
python comprehensive_test.py
```
This will generate detailed branch coverage reports with statistics.

#### 3. Run Unit Tests
```bash
python test_rs_codec.py
```
This will run all unit tests to verify the correctness of individual components.

### Advanced Usage

#### Custom Error Pattern Generation
```python
from error_generator import generate_systematic_error_patterns, generate_boundary_test_patterns
from rs_encoder import RSEncoder
from rs_decoder import RSDecoder

# Create encoder/decoder
encoder = RSEncoder(n=24, k=22)
decoder = RSDecoder(n=24, k=22)

# Generate custom error patterns
patterns = generate_systematic_error_patterns(24, 1)
boundary_patterns = generate_boundary_test_patterns(24, 1)

# Test with custom patterns
message = [1, 2, 3] * 7 + [1]  # 22 symbols
codeword = encoder.encode(message)

for pattern in patterns[:5]:  # Test first 5 patterns
    mask = pattern['mask']
    errored_codeword = [c ^ m for c, m in zip(codeword, mask)]
    try:
        decoded = decoder.decode(errored_codeword)
        print(f"Pattern {pattern['desc']}: SUCCESS")
    except Exception as e:
        print(f"Pattern {pattern['desc']}: ERROR - {e}")
```

#### Branch Coverage Analysis
```python
from branch_validator import BranchCoverageValidator
from error_patterns import ErrorPatternGenerator

# Generate comprehensive error patterns
generator = ErrorPatternGenerator(n=24, k=22, t=1)
patterns = []
patterns.extend(generator.generate_syndrome_patterns())
patterns.extend(generator.generate_locator_polynomial_patterns())
patterns.extend(generator.generate_boundary_patterns())

# Run coverage analysis
validator = BranchCoverageValidator()
results = validator.run_with_coverage(patterns)

print(f"Branch coverage: {results['branch_coverage']:.1f}%")
print(f"Covered branches: {results['coverage_report']['covered_branches']}")
```

## Project Structure

```
reedsolo/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── main.py                  # Basic branch validation tests
├── comprehensive_test.py    # Comprehensive coverage analysis
├── test_rs_codec.py        # Unit tests
├── rs_encoder.py           # Reed-Solomon encoder
├── rs_decoder.py           # Reed-Solomon decoder
├── gf.py                   # Galois Field GF(2^6) operations
├── error_generator.py      # Basic error pattern generation
├── error_patterns.py       # Systematic error pattern generation
├── branch_validator.py     # Branch coverage validation
└── venv/                   # Virtual environment (created after setup)
```

## Key Features

### 1. Complete Reed-Solomon Implementation
- **Encoder**: Systematic RS encoding with configurable parameters
- **Decoder**: Full RS decoding with error correction
- **Galois Field**: Complete GF(2^6) arithmetic operations

### 2. Comprehensive Error Pattern Generation
- **Basic Patterns**: No errors, correctable errors, uncorrectable errors
- **Systematic Patterns**: Position-specific, consecutive, boundary patterns
- **Advanced Patterns**: Syndrome-targeted, locator polynomial-targeted patterns

### 3. Branch Coverage Analysis
- **Target Branches**: 9 key decoder decision branches
- **Coverage Tracking**: Real-time branch hit analysis
- **Detailed Reports**: Per-configuration and overall statistics

### 4. Quality Assurance
- **Unit Tests**: 16 comprehensive test cases
- **Integration Tests**: End-to-end validation
- **Error Handling**: Robust exception management

## Supported Configurations

| Error Tolerance (t) | Info Symbols (k) | Total Symbols (n) | Use Case |
|:------------------:|:----------------:|:-----------------:|:---------|
| 1 | 22 | 24 | Small packets, low error rate |
| 1 | 44 | 46 | Medium packets, low error rate |
| 2 | 44 | 48 | Medium packets, high error rate |

## Branch Coverage Targets

The system aims to cover these key decoder branches:

1. **Syndrome Calculation** - Computing error syndromes
2. **Syndrome Zero Check** - Detecting error-free codewords
3. **Error Locator Degree 1** - Single error correction
4. **Error Locator Degree 2** - Double error correction
5. **Chien Search** - Finding error positions
6. **Forney Algorithm** - Computing error values
7. **Correction Verification** - Validating corrections
8. **Too Many Errors Exception** - Handling uncorrectable errors
9. **Correction Failed Exception** - Handling correction failures
