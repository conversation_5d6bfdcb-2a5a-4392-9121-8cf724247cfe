"""
Comprehensive test suite for RS decoder branch coverage.
"""
from error_patterns import ErrorPatternGenerator
from branch_validator import BranchCoverageValidator
from rs_encoder import RSEncoder
from rs_decoder import RSDecoder

def run_comprehensive_validation():
    """运行完整的分支覆盖验证"""
    configs = [(24, 22, 1), (46, 44, 1), (48, 44, 2)]

    overall_results = {}

    for n, k, t in configs:
        print(f"Testing configuration: n={n}, k={k}, t={t}")

        # 生成所有错误模式
        generator = ErrorPatternGenerator(n, k, t)
        patterns = []
        patterns.extend(generator.generate_syndrome_patterns())
        patterns.extend(generator.generate_locator_polynomial_patterns())
        patterns.extend(generator.generate_boundary_patterns())

        # 运行验证
        validator = BranchCoverageValidator()
        results = validator.run_with_coverage(patterns)

        overall_results[f"n{n}_k{k}_t{t}"] = results

        print(f"Branch coverage: {results['branch_coverage']:.1f}%")
        print(f"Tests passed: {sum(1 for r in results['test_results'].values() if 'error' not in r)}/{len(patterns)}")

    return overall_results

if __name__ == "__main__":
    results = run_comprehensive_validation()
    # 生成详细报告
    generate_coverage_report(results)