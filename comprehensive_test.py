"""
Comprehensive test suite for RS decoder branch coverage.
"""
from error_patterns import ErrorPatternGenerator
from branch_validator import BranchCoverageValidator
from rs_encoder import RSEncoder
from rs_decoder import RSDecoder

def run_comprehensive_validation():
    """运行完整的分支覆盖验证"""
    configs = [(24, 22, 1), (46, 44, 1), (48, 44, 2)]

    overall_results = {}

    for n, k, t in configs:
        print(f"Testing configuration: n={n}, k={k}, t={t}")

        # 生成所有错误模式
        generator = ErrorPatternGenerator(n, k, t)
        patterns = []
        patterns.extend(generator.generate_syndrome_patterns())
        patterns.extend(generator.generate_locator_polynomial_patterns())
        patterns.extend(generator.generate_boundary_patterns())

        # 运行验证
        validator = BranchCoverageValidator()
        results = validator.run_with_coverage(patterns)

        overall_results[f"n{n}_k{k}_t{t}"] = results

        print(f"Branch coverage: {results['branch_coverage']:.1f}%")
        print(f"Tests passed: {sum(1 for r in results['test_results'].values() if 'error' not in r)}/{len(patterns)}")

    return overall_results

def generate_coverage_report(results: dict):
    """生成详细的覆盖率报告"""
    print("\n" + "="*60)
    print("Reed-Solomon Decoder Branch Coverage Report")
    print("="*60)

    total_configs = len(results)
    total_coverage = 0
    total_tests = 0
    total_passed = 0

    for config_name, config_results in results.items():
        print(f"\nConfiguration: {config_name}")
        print("-" * 40)

        branch_coverage = config_results.get('branch_coverage', 0)
        test_results = config_results.get('test_results', {})
        coverage_report = config_results.get('coverage_report', {})

        passed_tests = sum(1 for r in test_results.values() if 'error' not in r)
        total_tests_config = len(test_results)

        print(f"Branch Coverage: {branch_coverage:.1f}%")
        print(f"Tests Passed: {passed_tests}/{total_tests_config}")

        # 显示覆盖的分支
        covered_branches = coverage_report.get('covered_branches', [])
        uncovered_branches = coverage_report.get('uncovered_branches', [])

        if covered_branches:
            print(f"Covered Branches ({len(covered_branches)}):")
            for branch in covered_branches:
                print(f"  ✓ {branch}")

        if uncovered_branches:
            print(f"Uncovered Branches ({len(uncovered_branches)}):")
            for branch in uncovered_branches:
                print(f"  ✗ {branch}")

        # 显示失败的测试
        failed_tests = [name for name, result in test_results.items() if 'error' in result]
        if failed_tests:
            print(f"Failed Tests ({len(failed_tests)}):")
            for test_name in failed_tests[:5]:  # 只显示前5个
                error_msg = test_results[test_name].get('error', 'Unknown error')
                print(f"  ✗ {test_name}: {error_msg}")
            if len(failed_tests) > 5:
                print(f"  ... and {len(failed_tests) - 5} more")

        total_coverage += branch_coverage
        total_tests += total_tests_config
        total_passed += passed_tests

    # 总体统计
    print("\n" + "="*60)
    print("Overall Summary")
    print("="*60)
    avg_coverage = total_coverage / total_configs if total_configs > 0 else 0
    print(f"Average Branch Coverage: {avg_coverage:.1f}%")
    print(f"Total Tests: {total_tests}")
    print(f"Total Passed: {total_passed}")
    print(f"Overall Pass Rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

if __name__ == "__main__":
    results = run_comprehensive_validation()
    # 生成详细报告
    generate_coverage_report(results)