"""
Module for generating specific error patterns to test decoder branches.
"""
import random
import itertools

def generate_no_error_mask(n):
    """Generates a mask representing no errors."""
    return {'desc': 'no_error', 'mask': [0] * n}

def generate_correctable_error_mask(n, t):
    """
    Generates a mask with a correctable number of errors (1 to t).
    """
    if t == 0:
        return generate_no_error_mask(n)

    num_errors = random.randint(1, t)
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'desc': f'correctable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'success'
    }

def generate_uncorrectable_too_many_errors_mask(n, t):
    """
    Generates a mask with more errors than the decoder can handle (t+1).
    This should trigger the "Too many errors to correct" ReedSolomonError.
    """
    if t + 1 > n:
        # Cannot create more errors than the message length
        return None

    # 确保生成足够多的错误
    num_errors = min(t + 1, n // 2)  # 不超过一半的符号
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        # 使用更大的错误值确保真正产生错误
        mask[pos] = random.randint(1, 63)

    return {
        'desc': f'uncorrectable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'too_many_errors'
    }

def generate_failed_correction_mask(codeword, encoder, t):
    """
    Tries to find an error pattern that causes the final check to fail.
    This is a heuristic approach, as these failures are complex.

    The strategy is to create a codeword with t+1 errors, and then
    slightly modify one of the error values. Sometimes this can confuse
    the decoder into mis-correcting the message.
    """
    n = len(codeword)
    if t + 1 > n:
        return None

    num_errors = t + 1
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    # Slightly alter one error value - this can sometimes cause miscorrection
    pos_to_alter = random.choice(error_positions)
    mask[pos_to_alter] ^= random.randint(1, 63)

    return {
        'desc': f'potential_failed_correction_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'correction_failed'
    }

def generate_systematic_error_patterns(n, t):
    """
    生成系统化的错误模式以覆盖更多分支
    """
    patterns = []

    # 1. 单个位置的所有可能错误值
    for pos in range(min(n, 5)):  # 限制数量避免过多
        for error_val in [1, 2, 4, 8, 16, 32]:  # 2的幂次
            mask = [0] * n
            mask[pos] = error_val
            patterns.append({
                'desc': f'single_error_pos_{pos}_val_{error_val}',
                'mask': mask,
                'expected_outcome': 'success'
            })

    # 2. 连续位置的错误
    for start_pos in range(min(n - t, 5)):
        mask = [0] * n
        for i in range(t):
            if start_pos + i < n:
                mask[start_pos + i] = random.randint(1, 63)
        patterns.append({
            'desc': f'consecutive_errors_start_{start_pos}',
            'mask': mask,
            'expected_outcome': 'success'
        })

    # 3. 特定模式的错误（用于测试特殊分支）
    if n >= 4:
        # 首尾错误
        mask = [0] * n
        mask[0] = random.randint(1, 63)
        mask[-1] = random.randint(1, 63)
        patterns.append({
            'desc': 'first_last_errors',
            'mask': mask,
            'expected_outcome': 'success' if t >= 2 else 'too_many_errors'
        })

    return patterns

def generate_boundary_test_patterns(n, t):
    """
    生成边界测试模式
    """
    patterns = []

    # 1. 恰好t个错误的所有组合（限制数量）
    if t <= 3:  # 避免组合爆炸
        import itertools
        for positions in list(itertools.combinations(range(n), t))[:10]:  # 限制前10个
            mask = [0] * n
            for pos in positions:
                mask[pos] = random.randint(1, 63)
            patterns.append({
                'desc': f'exactly_{t}_errors_pattern_{positions}',
                'mask': mask,
                'expected_outcome': 'success'
            })

    # 2. 超过容错能力的错误
    for extra_errors in range(1, min(4, n - t)):
        num_errors = t + extra_errors
        if num_errors <= n:
            error_positions = random.sample(range(n), num_errors)
            mask = [0] * n
            for pos in error_positions:
                mask[pos] = random.randint(1, 63)
            patterns.append({
                'desc': f'too_many_errors_{num_errors}',
                'mask': mask,
                'expected_outcome': 'too_many_errors'
            })

    return patterns


# ============================================================================
# 专门针对每个目标分支的错误模式生成函数
# ============================================================================

def generate_syndrome_calculation_pattern(n, k, t):
    """
    生成触发综合征计算分支的错误模式
    任何非零错误都会触发综合征计算
    """
    mask = [0] * n
    # 在第一个位置引入单个错误
    mask[0] = 1

    return {
        'type': 'syndrome_calculation_trigger',
        'desc': 'syndrome_calculation_trigger',
        'mask': mask,
        'target_branch': 'syndrome_calculation',
        'expected_outcome': 'success'
    }

def generate_syndrome_zero_check_pattern(n, k, t):
    """
    生成触发综合征零检查分支的错误模式
    无错误的码字会触发综合征零检查
    """
    return {
        'type': 'syndrome_zero_check_trigger',
        'desc': 'syndrome_zero_check_trigger',
        'mask': [0] * n,  # 无错误
        'target_branch': 'syndrome_zero_check',
        'expected_outcome': 'success'
    }

def generate_error_locator_degree_1_pattern(n, k, t):
    """
    生成触发一次错误定位多项式分支的错误模式
    单个错误会产生一次错误定位多项式
    """
    mask = [0] * n
    # 在随机位置引入单个错误
    error_pos = random.randint(0, n-1)
    mask[error_pos] = random.randint(1, 63)

    return {
        'type': f'error_locator_degree_1_pos_{error_pos}',
        'desc': f'error_locator_degree_1_pos_{error_pos}',
        'mask': mask,
        'target_branch': 'error_locator_degree_1',
        'expected_outcome': 'success'
    }

def generate_error_locator_degree_2_pattern(n, k, t):
    """
    生成触发二次错误定位多项式分支的错误模式
    两个错误会产生二次错误定位多项式
    """
    if t < 2:
        return None  # 容错能力不足，无法处理两个错误

    mask = [0] * n
    # 在两个不同位置引入错误
    error_positions = random.sample(range(n), 2)
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'type': f'error_locator_degree_2_pos_{error_positions}',
        'desc': f'error_locator_degree_2_pos_{error_positions}',
        'mask': mask,
        'target_branch': 'error_locator_degree_2',
        'expected_outcome': 'success'
    }

def generate_chien_search_pattern(n, k, t):
    """
    生成触发Chien搜索分支的错误模式
    任何可纠正的错误都会触发Chien搜索
    """
    num_errors = min(t, random.randint(1, t))
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'type': f'chien_search_trigger_{num_errors}_errors',
        'desc': f'chien_search_trigger_{num_errors}_errors',
        'mask': mask,
        'target_branch': 'chien_search',
        'expected_outcome': 'success'
    }

def generate_forney_algorithm_pattern(n, k, t):
    """
    生成触发Forney算法分支的错误模式
    找到错误位置后需要Forney算法计算错误值
    """
    num_errors = min(t, random.randint(1, t))
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'type': f'forney_algorithm_trigger_{num_errors}_errors',
        'desc': f'forney_algorithm_trigger_{num_errors}_errors',
        'mask': mask,
        'target_branch': 'forney_algorithm',
        'expected_outcome': 'success'
    }

def generate_correction_verification_pattern(n, k, t):
    """
    生成触发纠正验证分支的错误模式
    成功纠正后需要验证结果
    """
    num_errors = min(t, random.randint(1, t))
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'type': f'correction_verification_trigger_{num_errors}_errors',
        'desc': f'correction_verification_trigger_{num_errors}_errors',
        'mask': mask,
        'target_branch': 'correction_verification',
        'expected_outcome': 'success'
    }

def generate_too_many_errors_exception_pattern(n, k, t):
    """
    生成触发"太多错误"异常分支的错误模式
    超过容错能力的错误数量
    """
    # 确保错误数量超过容错能力
    num_errors = min(t + 2, n // 2)  # 至少比容错能力多2个
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'type': f'too_many_errors_exception_{num_errors}_errors',
        'desc': f'too_many_errors_exception_{num_errors}_errors',
        'mask': mask,
        'target_branch': 'too_many_errors_exception',
        'expected_outcome': 'too_many_errors'
    }

def generate_correction_failed_exception_pattern(n, k, t):
    """
    生成触发"纠正失败"异常分支的错误模式
    这种情况比较复杂，通常发生在错误模式导致解码器误判的情况
    """
    # 策略：创建一个复杂的错误模式，可能导致纠正失败
    num_errors = t + 1
    if num_errors > n:
        return None

    error_positions = random.sample(range(n), num_errors)
    mask = [0] * n

    # 使用特殊的错误值模式，可能导致纠正算法失败
    for i, pos in enumerate(error_positions):
        # 使用与位置相关的错误值
        mask[pos] = ((pos * 7 + i * 13) % 63) + 1

    return {
        'type': f'correction_failed_exception_{num_errors}_errors',
        'desc': f'correction_failed_exception_{num_errors}_errors',
        'mask': mask,
        'target_branch': 'correction_failed_exception',
        'expected_outcome': 'correction_failed'
    }


# ============================================================================
# 统一的分支目标错误模式生成器
# ============================================================================

def generate_all_branch_patterns(n, k, t):
    """
    生成针对所有目标分支的错误模式
    返回一个字典，键为分支名称，值为对应的错误模式
    """
    branch_generators = {
        'syndrome_calculation': generate_syndrome_calculation_pattern,
        'syndrome_zero_check': generate_syndrome_zero_check_pattern,
        'error_locator_degree_1': generate_error_locator_degree_1_pattern,
        'error_locator_degree_2': generate_error_locator_degree_2_pattern,
        'chien_search': generate_chien_search_pattern,
        'forney_algorithm': generate_forney_algorithm_pattern,
        'correction_verification': generate_correction_verification_pattern,
        'too_many_errors_exception': generate_too_many_errors_exception_pattern,
        'correction_failed_exception': generate_correction_failed_exception_pattern
    }

    patterns = {}
    for branch_name, generator_func in branch_generators.items():
        try:
            pattern = generator_func(n, k, t)
            if pattern is not None:
                patterns[branch_name] = pattern
        except Exception as e:
            print(f"Warning: Failed to generate pattern for {branch_name}: {e}")

    return patterns

def generate_multiple_patterns_per_branch(n, k, t, num_patterns_per_branch=3):
    """
    为每个分支生成多个不同的错误模式
    增加触发目标分支的概率
    """
    all_patterns = []

    # 为每个分支生成多个模式
    branch_generators = {
        'syndrome_calculation': generate_syndrome_calculation_pattern,
        'syndrome_zero_check': generate_syndrome_zero_check_pattern,
        'error_locator_degree_1': generate_error_locator_degree_1_pattern,
        'error_locator_degree_2': generate_error_locator_degree_2_pattern,
        'chien_search': generate_chien_search_pattern,
        'forney_algorithm': generate_forney_algorithm_pattern,
        'correction_verification': generate_correction_verification_pattern,
        'too_many_errors_exception': generate_too_many_errors_exception_pattern,
        'correction_failed_exception': generate_correction_failed_exception_pattern
    }

    for branch_name, generator_func in branch_generators.items():
        for i in range(num_patterns_per_branch):
            try:
                pattern = generator_func(n, k, t)
                if pattern is not None:
                    # 为每个模式添加序号以区分
                    pattern['desc'] = f"{pattern['desc']}_variant_{i+1}"
                    pattern['type'] = f"{pattern.get('type', pattern['desc'])}_variant_{i+1}"
                    all_patterns.append(pattern)
            except Exception as e:
                print(f"Warning: Failed to generate pattern {i+1} for {branch_name}: {e}")

    return all_patterns

def get_branch_specific_pattern(branch_name, n, k, t):
    """
    根据分支名称生成特定的错误模式
    """
    branch_generators = {
        'syndrome_calculation': generate_syndrome_calculation_pattern,
        'syndrome_zero_check': generate_syndrome_zero_check_pattern,
        'error_locator_degree_1': generate_error_locator_degree_1_pattern,
        'error_locator_degree_2': generate_error_locator_degree_2_pattern,
        'chien_search': generate_chien_search_pattern,
        'forney_algorithm': generate_forney_algorithm_pattern,
        'correction_verification': generate_correction_verification_pattern,
        'too_many_errors_exception': generate_too_many_errors_exception_pattern,
        'correction_failed_exception': generate_correction_failed_exception_pattern
    }

    if branch_name not in branch_generators:
        raise ValueError(f"Unknown branch name: {branch_name}")

    return branch_generators[branch_name](n, k, t)
