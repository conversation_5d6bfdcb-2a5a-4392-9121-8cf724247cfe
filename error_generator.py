"""
Module for generating specific error patterns to test decoder branches.
"""
import random
import itertools

def generate_no_error_mask(n):
    """Generates a mask representing no errors."""
    return {'desc': 'no_error', 'mask': [0] * n}

def generate_correctable_error_mask(n, t):
    """
    Generates a mask with a correctable number of errors (1 to t).
    """
    if t == 0:
        return generate_no_error_mask(n)
        
    num_errors = random.randint(1, t)
    error_positions = random.sample(range(n), num_errors)
    
    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)
        
    return {
        'desc': f'correctable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'success'
    }

def generate_uncorrectable_too_many_errors_mask(n, t):
    """
    Generates a mask with more errors than the decoder can handle (t+1).
    This should trigger the "Too many errors to correct" ReedSolomonError.
    """
    if t + 1 > n:
        # Cannot create more errors than the message length
        return None

    num_errors = t + 1
    error_positions = random.sample(range(n), num_errors)
    
    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)
        
    return {
        'desc': f'uncorrectable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'too_many_errors'
    }

def generate_failed_correction_mask(codeword, encoder, t):
    """
    Tries to find an error pattern that causes the final check to fail.
    This is a heuristic approach, as these failures are complex.
    
    The strategy is to create a codeword with t+1 errors, and then
    slightly modify one of the error values. Sometimes this can confuse
    the decoder into mis-correcting the message.
    """
    n = len(codeword)
    if t + 1 > n:
        return None

    num_errors = t + 1
    error_positions = random.sample(range(n), num_errors)
    
    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)
        
    # Slightly alter one error value - this can sometimes cause miscorrection
    pos_to_alter = random.choice(error_positions)
    mask[pos_to_alter] ^= random.randint(1, 63)

    return {
        'desc': f'potential_failed_correction_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'correction_failed'
    }
