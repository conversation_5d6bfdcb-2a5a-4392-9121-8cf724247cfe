"""
Module for generating specific error patterns to test decoder branches.
"""
import random
import itertools

def generate_no_error_mask(n):
    """Generates a mask representing no errors."""
    return {'desc': 'no_error', 'mask': [0] * n}

def generate_correctable_error_mask(n, t):
    """
    Generates a mask with a correctable number of errors (1 to t).
    """
    if t == 0:
        return generate_no_error_mask(n)

    num_errors = random.randint(1, t)
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    return {
        'desc': f'correctable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'success'
    }

def generate_uncorrectable_too_many_errors_mask(n, t):
    """
    Generates a mask with more errors than the decoder can handle (t+1).
    This should trigger the "Too many errors to correct" ReedSolomonError.
    """
    if t + 1 > n:
        # Cannot create more errors than the message length
        return None

    # 确保生成足够多的错误
    num_errors = min(t + 1, n // 2)  # 不超过一半的符号
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        # 使用更大的错误值确保真正产生错误
        mask[pos] = random.randint(1, 63)

    return {
        'desc': f'uncorrectable_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'too_many_errors'
    }

def generate_failed_correction_mask(codeword, encoder, t):
    """
    Tries to find an error pattern that causes the final check to fail.
    This is a heuristic approach, as these failures are complex.

    The strategy is to create a codeword with t+1 errors, and then
    slightly modify one of the error values. Sometimes this can confuse
    the decoder into mis-correcting the message.
    """
    n = len(codeword)
    if t + 1 > n:
        return None

    num_errors = t + 1
    error_positions = random.sample(range(n), num_errors)

    mask = [0] * n
    for pos in error_positions:
        mask[pos] = random.randint(1, 63)

    # Slightly alter one error value - this can sometimes cause miscorrection
    pos_to_alter = random.choice(error_positions)
    mask[pos_to_alter] ^= random.randint(1, 63)

    return {
        'desc': f'potential_failed_correction_{num_errors}_errors',
        'mask': mask,
        'expected_outcome': 'correction_failed'
    }

def generate_systematic_error_patterns(n, t):
    """
    生成系统化的错误模式以覆盖更多分支
    """
    patterns = []

    # 1. 单个位置的所有可能错误值
    for pos in range(min(n, 5)):  # 限制数量避免过多
        for error_val in [1, 2, 4, 8, 16, 32]:  # 2的幂次
            mask = [0] * n
            mask[pos] = error_val
            patterns.append({
                'desc': f'single_error_pos_{pos}_val_{error_val}',
                'mask': mask,
                'expected_outcome': 'success'
            })

    # 2. 连续位置的错误
    for start_pos in range(min(n - t, 5)):
        mask = [0] * n
        for i in range(t):
            if start_pos + i < n:
                mask[start_pos + i] = random.randint(1, 63)
        patterns.append({
            'desc': f'consecutive_errors_start_{start_pos}',
            'mask': mask,
            'expected_outcome': 'success'
        })

    # 3. 特定模式的错误（用于测试特殊分支）
    if n >= 4:
        # 首尾错误
        mask = [0] * n
        mask[0] = random.randint(1, 63)
        mask[-1] = random.randint(1, 63)
        patterns.append({
            'desc': 'first_last_errors',
            'mask': mask,
            'expected_outcome': 'success' if t >= 2 else 'too_many_errors'
        })

    return patterns

def generate_boundary_test_patterns(n, t):
    """
    生成边界测试模式
    """
    patterns = []

    # 1. 恰好t个错误的所有组合（限制数量）
    if t <= 3:  # 避免组合爆炸
        import itertools
        for positions in list(itertools.combinations(range(n), t))[:10]:  # 限制前10个
            mask = [0] * n
            for pos in positions:
                mask[pos] = random.randint(1, 63)
            patterns.append({
                'desc': f'exactly_{t}_errors_pattern_{positions}',
                'mask': mask,
                'expected_outcome': 'success'
            })

    # 2. 超过容错能力的错误
    for extra_errors in range(1, min(4, n - t)):
        num_errors = t + extra_errors
        if num_errors <= n:
            error_positions = random.sample(range(n), num_errors)
            mask = [0] * n
            for pos in error_positions:
                mask[pos] = random.randint(1, 63)
            patterns.append({
                'desc': f'too_many_errors_{num_errors}',
                'mask': mask,
                'expected_outcome': 'too_many_errors'
            })

    return patterns
