"""
Unit tests for Reed-Solomon encoder and decoder.
"""
import unittest
from rs_encoder import <PERSON>Encoder
from rs_decoder import RSDecoder
from gf import GF64, PRIMITIVE_POLY_INT, SYMBOL_BIT_LENGTH
import error_generator as eg


class TestGF64(unittest.TestCase):
    """Test cases for GF64 Galois field operations."""
    
    def setUp(self):
        self.gf = GF64()
    
    def test_field_size(self):
        """Test that field size is correct."""
        self.assertEqual(self.gf.field_size, 64)
        self.assertEqual(self.gf.prim_poly, 67)
    
    def test_alpha_power(self):
        """Test alpha power calculations."""
        # α^0 = 1
        self.assertEqual(self.gf.alpha_power(0), 1)
        # α^1 = 2 (primitive element)
        self.assertEqual(self.gf.alpha_power(1), 2)
    
    def test_multiply_by_alpha(self):
        """Test multiplication by alpha."""
        # 1 * α = 2
        self.assertEqual(self.gf._multiply_by_alpha(1), 2)
        # 2 * α = 4
        self.assertEqual(self.gf._multiply_by_alpha(2), 4)
    
    def test_multiply(self):
        """Test GF multiplication."""
        # 0 * anything = 0
        self.assertEqual(self.gf.multiply(0, 5), 0)
        self.assertEqual(self.gf.multiply(5, 0), 0)
        # 1 * anything = anything
        self.assertEqual(self.gf.multiply(1, 5), 5)
        self.assertEqual(self.gf.multiply(5, 1), 5)
    
    def test_divide(self):
        """Test GF division."""
        # 0 / anything = 0
        self.assertEqual(self.gf.divide(0, 5), 0)
        # anything / 1 = anything
        self.assertEqual(self.gf.divide(5, 1), 5)
        # Division by zero should raise error
        with self.assertRaises(ValueError):
            self.gf.divide(5, 0)
    
    def test_random_nonzero(self):
        """Test random nonzero element generation."""
        for _ in range(10):
            val = self.gf.random_nonzero()
            self.assertGreaterEqual(val, 1)
            self.assertLess(val, 64)


class TestRSEncoder(unittest.TestCase):
    """Test cases for Reed-Solomon encoder."""
    
    def test_encoder_initialization(self):
        """Test encoder initialization with different parameters."""
        # Valid configurations
        encoder1 = RSEncoder(n=24, k=22)
        self.assertEqual(encoder1.n, 24)
        self.assertEqual(encoder1.k, 22)
        self.assertEqual(encoder1.nsym, 2)
        
        encoder2 = RSEncoder(n=48, k=44)
        self.assertEqual(encoder2.n, 48)
        self.assertEqual(encoder2.k, 44)
        self.assertEqual(encoder2.nsym, 4)
        
        # Invalid configuration
        with self.assertRaises(ValueError):
            RSEncoder(n=22, k=24)  # n <= k
    
    def test_encode_message_length(self):
        """Test encoding with correct and incorrect message lengths."""
        encoder = RSEncoder(n=24, k=22)
        
        # Correct length
        message = [1, 2, 3] * 7 + [1]  # 22 symbols
        codeword = encoder.encode(message)
        self.assertEqual(len(codeword), 24)
        
        # Incorrect length
        with self.assertRaises(ValueError):
            encoder.encode([1, 2, 3])  # Too short
        
        with self.assertRaises(ValueError):
            encoder.encode([1] * 25)  # Too long
    
    def test_encode_systematic(self):
        """Test that encoding produces systematic codewords."""
        encoder = RSEncoder(n=24, k=22)
        message = [(i % 63) + 1 for i in range(22)]
        codeword = encoder.encode(message)
        
        # First k symbols should be the original message
        self.assertEqual(codeword[:22], message)
        # Last n-k symbols should be parity
        self.assertEqual(len(codeword[22:]), 2)


class TestRSDecoder(unittest.TestCase):
    """Test cases for Reed-Solomon decoder."""
    
    def test_decoder_initialization(self):
        """Test decoder initialization."""
        decoder = RSDecoder(n=24, k=22)
        self.assertEqual(decoder.n, 24)
        self.assertEqual(decoder.k, 22)
        self.assertEqual(decoder.nsym, 2)
    
    def test_decode_no_errors(self):
        """Test decoding without errors."""
        encoder = RSEncoder(n=24, k=22)
        decoder = RSDecoder(n=24, k=22)
        
        message = [(i % 63) + 1 for i in range(22)]
        codeword = encoder.encode(message)
        decoded = decoder.decode(codeword)
        
        self.assertEqual(decoded, message)
    
    def test_decode_with_correctable_errors(self):
        """Test decoding with correctable errors."""
        encoder = RSEncoder(n=24, k=22)
        decoder = RSDecoder(n=24, k=22)
        
        message = [(i % 63) + 1 for i in range(22)]
        codeword = encoder.encode(message)
        
        # Introduce 1 error (should be correctable for t=1)
        errored_codeword = codeword.copy()
        errored_codeword[0] ^= 5  # XOR with 5
        
        decoded = decoder.decode(errored_codeword)
        self.assertEqual(decoded, message)
    
    def test_decode_message_length(self):
        """Test decoding with incorrect message length."""
        decoder = RSDecoder(n=24, k=22)
        
        with self.assertRaises(ValueError):
            decoder.decode([1, 2, 3])  # Too short
        
        with self.assertRaises(ValueError):
            decoder.decode([1] * 25)  # Too long


class TestErrorGenerator(unittest.TestCase):
    """Test cases for error pattern generation."""
    
    def test_no_error_mask(self):
        """Test no error mask generation."""
        mask_info = eg.generate_no_error_mask(24)
        self.assertEqual(mask_info['desc'], 'no_error')
        self.assertEqual(mask_info['mask'], [0] * 24)
    
    def test_correctable_error_mask(self):
        """Test correctable error mask generation."""
        mask_info = eg.generate_correctable_error_mask(24, 1)
        self.assertIn('correctable', mask_info['desc'])
        self.assertEqual(len(mask_info['mask']), 24)
        self.assertEqual(mask_info['expected_outcome'], 'success')
        
        # Should have at most t errors
        error_count = sum(1 for x in mask_info['mask'] if x != 0)
        self.assertLessEqual(error_count, 1)
    
    def test_uncorrectable_error_mask(self):
        """Test uncorrectable error mask generation."""
        mask_info = eg.generate_uncorrectable_too_many_errors_mask(24, 1)
        self.assertIn('uncorrectable', mask_info['desc'])
        self.assertEqual(len(mask_info['mask']), 24)
        self.assertEqual(mask_info['expected_outcome'], 'too_many_errors')
        
        # Should have more than t errors
        error_count = sum(1 for x in mask_info['mask'] if x != 0)
        self.assertGreater(error_count, 1)


if __name__ == '__main__':
    unittest.main()
